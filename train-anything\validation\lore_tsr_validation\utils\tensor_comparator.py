"""
LORE-TSR张量精确对比工具

这个模块提供专门针对LORE-TSR迁移的张量精确对比功能，包括：
- 多维度张量对比：形状、类型、数值、统计特性
- 精确容忍度控制：绝对差异和相对差异
- 详细报告：差异的具体位置和数值分析
- 可视化支持：生成差异热图和统计图表

设计原则：
- 精确性：提供严格的数值精度控制
- 详细性：提供完整的对比过程和结果
- 可视化：支持差异的图形化展示
- 可扩展性：支持新的对比需求

作者: LORE-TSR迁移团队
版本: v1.0
创建日期: 2025-07-21
"""

import torch
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

from ...common.comparison_engine import ComparisonEngine, ComparisonResult, ComparisonStatus


@dataclass
class TensorComparisonReport:
    """张量对比报告"""
    tensor_name: str
    comparison_result: ComparisonResult
    shape_info: Dict[str, Any]
    dtype_info: Dict[str, Any]
    statistical_analysis: Dict[str, Any]
    difference_analysis: Dict[str, Any]
    visualization_paths: List[str] = None
    
    def __post_init__(self):
        if self.visualization_paths is None:
            self.visualization_paths = []


class TensorComparator:
    """
    张量精确对比工具
    
    专门针对LORE-TSR迁移的张量对比，提供多维度的精确分析。
    """
    
    def __init__(self, absolute_tolerance: float = 1e-6, 
                 relative_tolerance: float = 1e-5,
                 enable_visualization: bool = True):
        """
        初始化张量对比器
        
        Args:
            absolute_tolerance: 绝对差异容忍度
            relative_tolerance: 相对差异容忍度
            enable_visualization: 是否启用可视化
        """
        self.comparison_engine = ComparisonEngine(absolute_tolerance, relative_tolerance)
        self.enable_visualization = enable_visualization
        self.reports = []
    
    def compare_tensors(self, tensor1: torch.Tensor, tensor2: torch.Tensor, 
                       tensor_name: str = "tensor",
                       save_visualization: bool = False,
                       output_dir: Optional[str] = None) -> TensorComparisonReport:
        """
        对比两个张量并生成详细报告
        
        Args:
            tensor1: 第一个张量（原始LORE-TSR）
            tensor2: 第二个张量（迁移版本）
            tensor_name: 张量名称
            save_visualization: 是否保存可视化结果
            output_dir: 输出目录
            
        Returns:
            详细的张量对比报告
        """
        # 基础对比
        comparison_result = self.comparison_engine.compare_basic(tensor1, tensor2)
        
        # 形状信息分析
        shape_info = self._analyze_shape_info(tensor1, tensor2)
        
        # 数据类型信息分析
        dtype_info = self._analyze_dtype_info(tensor1, tensor2)
        
        # 统计特性分析
        statistical_analysis = self._analyze_statistical_properties(tensor1, tensor2)
        
        # 差异分析
        difference_analysis = self._analyze_differences(tensor1, tensor2)
        
        # 创建报告
        report = TensorComparisonReport(
            tensor_name=tensor_name,
            comparison_result=comparison_result,
            shape_info=shape_info,
            dtype_info=dtype_info,
            statistical_analysis=statistical_analysis,
            difference_analysis=difference_analysis
        )
        
        # 生成可视化
        if self.enable_visualization and save_visualization:
            visualization_paths = self._generate_visualizations(
                tensor1, tensor2, tensor_name, output_dir
            )
            report.visualization_paths = visualization_paths
        
        self.reports.append(report)
        return report
    
    def _analyze_shape_info(self, tensor1: torch.Tensor, tensor2: torch.Tensor) -> Dict[str, Any]:
        """分析张量形状信息"""
        return {
            "tensor1_shape": list(tensor1.shape),
            "tensor2_shape": list(tensor2.shape),
            "shape_match": tensor1.shape == tensor2.shape,
            "tensor1_numel": tensor1.numel(),
            "tensor2_numel": tensor2.numel(),
            "tensor1_ndim": tensor1.ndim,
            "tensor2_ndim": tensor2.ndim
        }
    
    def _analyze_dtype_info(self, tensor1: torch.Tensor, tensor2: torch.Tensor) -> Dict[str, Any]:
        """分析张量数据类型信息"""
        return {
            "tensor1_dtype": str(tensor1.dtype),
            "tensor2_dtype": str(tensor2.dtype),
            "dtype_match": tensor1.dtype == tensor2.dtype,
            "tensor1_device": str(tensor1.device),
            "tensor2_device": str(tensor2.device),
            "device_match": tensor1.device == tensor2.device
        }
    
    def _analyze_statistical_properties(self, tensor1: torch.Tensor, 
                                      tensor2: torch.Tensor) -> Dict[str, Any]:
        """分析张量统计特性"""
        try:
            stats = {}
            
            # 基本统计
            for i, tensor in enumerate([tensor1, tensor2], 1):
                prefix = f"tensor{i}"
                stats[f"{prefix}_mean"] = float(torch.mean(tensor))
                stats[f"{prefix}_std"] = float(torch.std(tensor))
                stats[f"{prefix}_min"] = float(torch.min(tensor))
                stats[f"{prefix}_max"] = float(torch.max(tensor))
                stats[f"{prefix}_median"] = float(torch.median(tensor))
                
                # 分位数
                stats[f"{prefix}_q25"] = float(torch.quantile(tensor, 0.25))
                stats[f"{prefix}_q75"] = float(torch.quantile(tensor, 0.75))
                
                # 零值和非零值统计
                stats[f"{prefix}_zeros"] = int(torch.sum(tensor == 0))
                stats[f"{prefix}_nonzeros"] = int(torch.sum(tensor != 0))
                
                # NaN和Inf检查
                stats[f"{prefix}_has_nan"] = bool(torch.isnan(tensor).any())
                stats[f"{prefix}_has_inf"] = bool(torch.isinf(tensor).any())
            
            # 差异统计
            if tensor1.shape == tensor2.shape:
                diff = tensor1 - tensor2
                stats["diff_mean"] = float(torch.mean(diff))
                stats["diff_std"] = float(torch.std(diff))
                stats["diff_min"] = float(torch.min(diff))
                stats["diff_max"] = float(torch.max(diff))
                stats["diff_abs_mean"] = float(torch.mean(torch.abs(diff)))
                stats["diff_abs_max"] = float(torch.max(torch.abs(diff)))
                
                # 相关性分析
                if tensor1.numel() > 1:
                    flat1 = tensor1.flatten()
                    flat2 = tensor2.flatten()
                    correlation = torch.corrcoef(torch.stack([flat1, flat2]))[0, 1]
                    stats["correlation"] = float(correlation) if not torch.isnan(correlation) else 0.0
            
            return stats
            
        except Exception as e:
            return {"error": f"统计分析失败: {e}"}
    
    def _analyze_differences(self, tensor1: torch.Tensor, tensor2: torch.Tensor) -> Dict[str, Any]:
        """分析张量差异"""
        try:
            if tensor1.shape != tensor2.shape:
                return {"error": "张量形状不匹配，无法进行差异分析"}
            
            diff = tensor1 - tensor2
            abs_diff = torch.abs(diff)
            
            analysis = {}
            
            # 差异分布
            analysis["total_elements"] = tensor1.numel()
            analysis["identical_elements"] = int(torch.sum(diff == 0))
            analysis["different_elements"] = int(torch.sum(diff != 0))
            analysis["difference_ratio"] = float(analysis["different_elements"] / analysis["total_elements"])
            
            # 差异阈值分析
            thresholds = [1e-8, 1e-7, 1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1]
            for threshold in thresholds:
                count = int(torch.sum(abs_diff > threshold))
                analysis[f"elements_above_{threshold}"] = count
                analysis[f"ratio_above_{threshold}"] = float(count / analysis["total_elements"])
            
            # 最大差异位置
            max_diff_idx = torch.argmax(abs_diff)
            max_diff_pos = torch.unravel_index(max_diff_idx, tensor1.shape)
            analysis["max_diff_position"] = [int(p) for p in max_diff_pos]
            analysis["max_diff_value"] = float(abs_diff[max_diff_pos])
            analysis["tensor1_at_max_diff"] = float(tensor1[max_diff_pos])
            analysis["tensor2_at_max_diff"] = float(tensor2[max_diff_pos])
            
            return analysis
            
        except Exception as e:
            return {"error": f"差异分析失败: {e}"}
    
    def _generate_visualizations(self, tensor1: torch.Tensor, tensor2: torch.Tensor,
                               tensor_name: str, output_dir: Optional[str] = None) -> List[str]:
        """生成可视化图表"""
        if not self.enable_visualization:
            return []
        
        try:
            if output_dir is None:
                output_dir = "validation/lore_tsr_validation/reports/visualizations"
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            visualization_paths = []
            
            # 只对2D张量生成热图
            if tensor1.ndim == 2 and tensor2.ndim == 2 and tensor1.shape == tensor2.shape:
                # 差异热图
                diff = (tensor1 - tensor2).detach().cpu().numpy()
                plt.figure(figsize=(12, 4))
                
                plt.subplot(1, 3, 1)
                plt.imshow(tensor1.detach().cpu().numpy(), cmap='viridis')
                plt.title(f'{tensor_name} - Original')
                plt.colorbar()
                
                plt.subplot(1, 3, 2)
                plt.imshow(tensor2.detach().cpu().numpy(), cmap='viridis')
                plt.title(f'{tensor_name} - Migrated')
                plt.colorbar()
                
                plt.subplot(1, 3, 3)
                plt.imshow(diff, cmap='RdBu_r')
                plt.title(f'{tensor_name} - Difference')
                plt.colorbar()
                
                heatmap_path = output_path / f"{tensor_name}_heatmap.png"
                plt.tight_layout()
                plt.savefig(heatmap_path, dpi=150, bbox_inches='tight')
                plt.close()
                visualization_paths.append(str(heatmap_path))
            
            # 统计分布图
            plt.figure(figsize=(12, 8))
            
            # 直方图对比
            plt.subplot(2, 2, 1)
            data1 = tensor1.detach().cpu().numpy().flatten()
            data2 = tensor2.detach().cpu().numpy().flatten()
            plt.hist(data1, bins=50, alpha=0.7, label='Original', density=True)
            plt.hist(data2, bins=50, alpha=0.7, label='Migrated', density=True)
            plt.xlabel('Value')
            plt.ylabel('Density')
            plt.title(f'{tensor_name} - Value Distribution')
            plt.legend()
            
            # 差异分布
            if tensor1.shape == tensor2.shape:
                plt.subplot(2, 2, 2)
                diff_data = (tensor1 - tensor2).detach().cpu().numpy().flatten()
                plt.hist(diff_data, bins=50, alpha=0.7, color='red')
                plt.xlabel('Difference')
                plt.ylabel('Count')
                plt.title(f'{tensor_name} - Difference Distribution')
            
            # 散点图对比
            if tensor1.numel() <= 10000:  # 限制点数避免过于密集
                plt.subplot(2, 2, 3)
                plt.scatter(data1, data2, alpha=0.5, s=1)
                plt.xlabel('Original Values')
                plt.ylabel('Migrated Values')
                plt.title(f'{tensor_name} - Value Correlation')
                # 添加y=x参考线
                min_val = min(data1.min(), data2.min())
                max_val = max(data1.max(), data2.max())
                plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
            
            # 统计摘要
            plt.subplot(2, 2, 4)
            stats_text = f"""
            Original: mean={data1.mean():.6f}, std={data1.std():.6f}
            Migrated: mean={data2.mean():.6f}, std={data2.std():.6f}
            Max abs diff: {np.abs(diff_data).max():.2e}
            Mean abs diff: {np.abs(diff_data).mean():.2e}
            """
            plt.text(0.1, 0.5, stats_text, transform=plt.gca().transAxes, 
                    fontsize=10, verticalalignment='center')
            plt.axis('off')
            plt.title(f'{tensor_name} - Statistics Summary')
            
            stats_path = output_path / f"{tensor_name}_statistics.png"
            plt.tight_layout()
            plt.savefig(stats_path, dpi=150, bbox_inches='tight')
            plt.close()
            visualization_paths.append(str(stats_path))
            
            return visualization_paths
            
        except Exception as e:
            print(f"可视化生成失败: {e}")
            return []
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """生成汇总报告"""
        if not self.reports:
            return {"error": "没有可用的对比报告"}
        
        summary = {
            "total_comparisons": len(self.reports),
            "successful_comparisons": 0,
            "failed_comparisons": 0,
            "within_tolerance": 0,
            "exceeds_tolerance": 0,
            "shape_mismatches": 0,
            "type_mismatches": 0
        }
        
        for report in self.reports:
            result = report.comparison_result
            if result.is_success:
                summary["successful_comparisons"] += 1
                if result.status == ComparisonStatus.WITHIN_TOLERANCE:
                    summary["within_tolerance"] += 1
            else:
                summary["failed_comparisons"] += 1
                if result.status == ComparisonStatus.EXCEEDS_TOLERANCE:
                    summary["exceeds_tolerance"] += 1
                elif result.status == ComparisonStatus.SHAPE_MISMATCH:
                    summary["shape_mismatches"] += 1
                elif result.status == ComparisonStatus.TYPE_MISMATCH:
                    summary["type_mismatches"] += 1
        
        return summary
    
    def clear_reports(self):
        """清空报告历史"""
        self.reports.clear()
